# Docker Setup Guide for Kingdom SIS

This guide will help you set up PostgreSQL 17 and other services using Docker Compose.

## Prerequisites

- Docker installed on your system
- Docker Compose installed
- Node.js and npm/yarn for the Next.js application

## Quick Start

### 1. Start the Database

```bash
# Start PostgreSQL and other services
docker-compose up -d

# Check if services are running
docker-compose ps

# View logs
docker-compose logs postgres
```

### 2. Verify Database Connection

```bash
# Connect to PostgreSQL using psql
docker exec -it kingdom_sis_postgres psql -U kingdom_user -d kingdom_sis

# Or use the connection string from your app
# DATABASE_URL="postgresql://kingdom_user:kingdom_password_2024@localhost:5432/kingdom_sis"
```

### 3. Access pgAdmin (Optional)

- Open http://localhost:5050 in your browser
- Login with:
  - Email: `<EMAIL>`
  - Password: `admin123`

## Services Included

### PostgreSQL 17
- **Port**: 5432
- **Database**: kingdom_sis
- **Username**: kingdom_user
- **Password**: kingdom_password_2024
- **Container**: kingdom_sis_postgres

### pgAdmin 4 (Optional)
- **Port**: 5050
- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin123

### Redis (Optional)
- **Port**: 6379
- **Password**: redis_password_2024
- **Container**: kingdom_sis_redis

## Database Features

### Pre-configured Schema
- `kingdom_sis` - Main application schema
- `audit` - Audit logging schema

### Extensions Enabled
- `uuid-ossp` - UUID generation
- `pgcrypto` - Cryptographic functions

### Enum Types
- `user_role` - admin, teacher, student, parent
- `student_status` - active, inactive, suspended, graduated
- `enrollment_type` - fulltime, parttime, online, hybrid

### Audit Logging
- Automatic audit trail for all table changes
- Tracks INSERT, UPDATE, DELETE operations
- Stores old and new values in JSON format

## Environment Variables

Your `.env` file contains:

```env
DATABASE_URL="postgresql://kingdom_user:kingdom_password_2024@localhost:5432/kingdom_sis"
POSTGRES_DB=kingdom_sis
POSTGRES_USER=kingdom_user
POSTGRES_PASSWORD=kingdom_password_2024
REDIS_URL="redis://:redis_password_2024@localhost:6379"
```

## Common Commands

### Start Services
```bash
docker-compose up -d
```

### Stop Services
```bash
docker-compose down
```

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs postgres
docker-compose logs pgadmin
```

### Database Operations
```bash
# Connect to database
docker exec -it kingdom_sis_postgres psql -U kingdom_user -d kingdom_sis

# Backup database
docker exec kingdom_sis_postgres pg_dump -U kingdom_user kingdom_sis > backup.sql

# Restore database
docker exec -i kingdom_sis_postgres psql -U kingdom_user -d kingdom_sis < backup.sql

# Reset database (WARNING: This will delete all data)
docker-compose down -v
docker-compose up -d
```

### Redis Operations
```bash
# Connect to Redis
docker exec -it kingdom_sis_redis redis-cli -a redis_password_2024

# Monitor Redis
docker exec -it kingdom_sis_redis redis-cli -a redis_password_2024 monitor
```

## Troubleshooting

### Database Connection Issues

1. **Check if PostgreSQL is running**:
   ```bash
   docker-compose ps postgres
   ```

2. **Check PostgreSQL logs**:
   ```bash
   docker-compose logs postgres
   ```

3. **Test connection**:
   ```bash
   docker exec kingdom_sis_postgres pg_isready -U kingdom_user -d kingdom_sis
   ```

### Port Conflicts

If you get port conflicts, you can change the ports in `docker-compose.yml`:

```yaml
services:
  postgres:
    ports:
      - "5433:5432"  # Change from 5432 to 5433
```

Then update your `DATABASE_URL` accordingly.

### Permission Issues

If you encounter permission issues:

```bash
# Fix volume permissions
sudo chown -R $USER:$USER postgres_data
sudo chown -R $USER:$USER pgadmin_data
```

## Production Considerations

For production deployment:

1. **Change default passwords** in both `docker-compose.yml` and `.env`
2. **Use Docker secrets** for sensitive data
3. **Set up SSL/TLS** for database connections
4. **Configure backup strategy**
5. **Set up monitoring** and logging
6. **Use external volumes** for data persistence

## Integration with Next.js

Your Next.js application can connect to the database using:

```typescript
// lib/db.ts
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export default pool;
```

## Sample Database Queries

```sql
-- Check database status
SELECT version();

-- List all tables
\dt kingdom_sis.*

-- Check user roles
SELECT * FROM kingdom_sis.users;

-- Check audit logs
SELECT * FROM audit.audit_log ORDER BY changed_at DESC LIMIT 10;
```

## Next Steps

1. Start the services: `docker-compose up -d`
2. Verify database connection in your Next.js app
3. Run database migrations (if using Prisma/Drizzle)
4. Set up your ORM/database client
5. Begin developing your application!
