async function fetchStudents(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: Student[]; pageCount: number }> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Mock data
  const allStudents: Student[] = Array.from({ length: 200 }, (_, i) => ({
    id: `student-${i + 1}`,
    name: `Student ${i + 1}`,
    email: `student${i + 1}@university.edu`,
    status: ["active", "inactive", "suspended", "graduated"][i % 4],
    grade: ["A", "B", "C", "D", "F"][i % 5],
    department: ["cs", "math", "physics", "chemistry", "biology", "english"][
      i % 6
    ],
    enrollmentType: ["fulltime", "parttime", "online", "hybrid"][i % 4],
    year: (i % 5) + 1,
  }));

  // Apply filters and pagination (normally done on server)
  let filteredStudents = allStudents;

  params.columnFilters.forEach((filter) => {
    if (filter.value) {
      if (filter.id === "name" && typeof filter.value === "string") {
        filteredStudents = filteredStudents.filter((student) =>
          student.name
            .toLowerCase()
            .includes((filter.value as string).toLowerCase())
        );
      } else if (Array.isArray(filter.value) && filter.value.length > 0) {
        filteredStudents = filteredStudents.filter((student) =>
          (filter.value as string[]).includes(
            String(student[filter.id as keyof Student])
          )
        );
      }
    }
  });

  const { pageIndex, pageSize } = params.pagination;
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedStudents = filteredStudents.slice(start, end);

  return {
    data: paginatedStudents,
    pageCount: Math.ceil(filteredStudents.length / pageSize),
  };
}

export async function getStudents({}: {}) {
  const students = await prisma.student.findMany({
    include: {
      user: true,
      department: true,
    },
  });

  return students;
}
