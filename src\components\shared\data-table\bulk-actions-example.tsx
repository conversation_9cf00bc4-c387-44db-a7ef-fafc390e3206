"use client";

import * as React from "react";
import { ColumnDef, ColumnFiltersState, PaginationState, SortingState } from "@tanstack/react-table";
import { DataTable } from "./data-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "./data-table-column-header";
import { 
  Trash2, 
  Edit, 
  Mail, 
  Download, 
  Archive, 
  UserCheck, 
  UserX,
  GraduationCap,
  AlertTriangle
} from "lucide-react";
import { filterConfigs } from "@/app/(dashboard)/[school]/academics/filtering";
import { toast } from "sonner";

// Example data type
interface Student {
  id: string;
  name: string;
  email: string;
  status: string;
  grade: string;
  department: string;
  enrollmentType: string;
  year: number;
}

// Bulk actions configuration
const bulkActions = [
  {
    key: "activate",
    label: "Activate Selected",
    icon: <UserCheck className="h-4 w-4" />,
    variant: "default" as const,
    onClick: (selectedRows: Student[]) => {
      console.log("Activating students:", selectedRows);
      toast.success(`Activated ${selectedRows.length} students`);
    },
  },
  {
    key: "deactivate", 
    label: "Deactivate Selected",
    icon: <UserX className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: Student[]) => {
      console.log("Deactivating students:", selectedRows);
      toast.warning(`Deactivated ${selectedRows.length} students`);
    },
  },
  {
    key: "send-email",
    label: "Send Email",
    icon: <Mail className="h-4 w-4" />,
    variant: "secondary" as const,
    onClick: (selectedRows: Student[]) => {
      console.log("Sending email to students:", selectedRows);
      toast.info(`Sending email to ${selectedRows.length} students`);
    },
  },
  {
    key: "export",
    label: "Export Selected",
    icon: <Download className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: Student[]) => {
      console.log("Exporting students:", selectedRows);
      // Simulate CSV export
      const csvContent = [
        "Name,Email,Status,Grade,Department",
        ...selectedRows.map(student => 
          `${student.name},${student.email},${student.status},${student.grade},${student.department}`
        )
      ].join("\n");
      
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "selected-students.csv";
      a.click();
      window.URL.revokeObjectURL(url);
      
      toast.success(`Exported ${selectedRows.length} students to CSV`);
    },
  },
  {
    key: "archive",
    label: "Archive Selected",
    icon: <Archive className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: Student[]) => {
      console.log("Archiving students:", selectedRows);
      toast.info(`Archived ${selectedRows.length} students`);
    },
  },
  {
    key: "graduate",
    label: "Mark as Graduated",
    icon: <GraduationCap className="h-4 w-4" />,
    variant: "default" as const,
    onClick: (selectedRows: Student[]) => {
      console.log("Graduating students:", selectedRows);
      toast.success(`Marked ${selectedRows.length} students as graduated`);
    },
  },
  {
    key: "delete",
    label: "Delete Selected",
    icon: <Trash2 className="h-4 w-4" />,
    variant: "destructive" as const,
    onClick: (selectedRows: Student[]) => {
      if (confirm(`Are you sure you want to delete ${selectedRows.length} students? This action cannot be undone.`)) {
        console.log("Deleting students:", selectedRows);
        toast.error(`Deleted ${selectedRows.length} students`);
      }
    },
  },
];

// Columns definition with selection
const columns: ColumnDef<Student>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-2">
          <span className="font-medium">{row.getValue("name")}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const statusConfig = filterConfigs[0].options.find(opt => opt.value === status);
      
      const getStatusVariant = (status: string) => {
        switch (status) {
          case "active": return "default";
          case "inactive": return "secondary";
          case "suspended": return "destructive";
          case "graduated": return "outline";
          default: return "secondary";
        }
      };
      
      return (
        <div className="flex items-center gap-2">
          {statusConfig?.icon && <statusConfig.icon className="h-4 w-4" />}
          <Badge variant={getStatusVariant(status) as any}>
            {statusConfig?.label || status}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "grade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Grade" />
    ),
    cell: ({ row }) => {
      const grade = row.getValue("grade") as string;
      return <Badge variant="outline">{grade}</Badge>;
    },
  },
  {
    accessorKey: "department",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Department" />
    ),
    cell: ({ row }) => {
      const dept = row.getValue("department") as string;
      const deptConfig = filterConfigs[2].options.find(opt => opt.value === dept);
      return <span className="text-sm">{deptConfig?.label || dept}</span>;
    },
  },
  {
    accessorKey: "enrollmentType",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Enrollment" />
    ),
    cell: ({ row }) => {
      const type = row.getValue("enrollmentType") as string;
      const typeConfig = filterConfigs[3].options.find(opt => opt.value === type);
      return <span className="text-sm">{typeConfig?.label || type}</span>;
    },
  },
];

// Mock API function
async function fetchStudents(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: Student[]; pageCount: number }> {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock data
  const allStudents: Student[] = Array.from({ length: 100 }, (_, i) => ({
    id: `student-${i + 1}`,
    name: `Student ${i + 1}`,
    email: `student${i + 1}@university.edu`,
    status: ["active", "inactive", "suspended", "graduated"][i % 4],
    grade: ["A", "B", "C", "D", "F"][i % 5],
    department: ["cs", "math", "physics", "chemistry", "biology", "english"][i % 6],
    enrollmentType: ["fulltime", "parttime", "online", "hybrid"][i % 4],
    year: (i % 5) + 1,
  }));

  // Apply filters and pagination (normally done on server)
  let filteredStudents = allStudents;
  
  params.columnFilters.forEach(filter => {
    if (filter.value) {
      if (filter.id === "name" && typeof filter.value === "string") {
        filteredStudents = filteredStudents.filter(student => 
          student.name.toLowerCase().includes(filter.value.toLowerCase())
        );
      } else if (Array.isArray(filter.value) && filter.value.length > 0) {
        filteredStudents = filteredStudents.filter(student => 
          filter.value.includes(String(student[filter.id as keyof Student]))
        );
      }
    }
  });

  const { pageIndex, pageSize } = params.pagination;
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedStudents = filteredStudents.slice(start, end);

  return {
    data: paginatedStudents,
    pageCount: Math.ceil(filteredStudents.length / pageSize),
  };
}

export function BulkActionsExample() {
  const [data, setData] = React.useState<Student[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pageCount, setPageCount] = React.useState(-1);
  
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchStudents({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Bulk Actions Example</h1>
        <p className="text-muted-foreground">
          Select multiple rows to see bulk actions in action. Try selecting students and using the bulk action buttons.
        </p>
      </div>
      
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search students by name..."
        enableBulkActions={true}
        bulkActions={bulkActions}
      />
    </div>
  );
}
