"use client";

import * as React from "react";
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { DataTable } from "@/components/shared/data-table/data-table";
import { columns } from "./columns";
import { studentBulkActions } from "./bulk-actions";

// Import your filter options (you'll need to create these)
const statusOptions = [
  { label: "Backlog", value: "backlog" },
  { label: "Todo", value: "todo" },
  { label: "In Progress", value: "in-progress" },
  { label: "Done", value: "done" },
  { label: "Canceled", value: "canceled" },
];

const priorityOptions = [
  { label: "Low", value: "low" },
  { label: "Medium", value: "medium" },
  { label: "High", value: "high" },
];

// Server-side fetch function
async function fetchTasks(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}) {
  // Convert TanStack params to your API format
  const queryParams = new URLSearchParams({
    page: (params.pagination.pageIndex + 1).toString(),
    pageSize: params.pagination.pageSize.toString(),
  });

  // Add sorting
  if (params.sorting.length > 0) {
    const sort = params.sorting[0];
    queryParams.append("sortBy", sort.id);
    queryParams.append("sortOrder", sort.desc ? "desc" : "asc");
  }

  // Add filters
  params.columnFilters.forEach((filter) => {
    if (filter.value) {
      if (Array.isArray(filter.value)) {
        filter.value.forEach((val) =>
          queryParams.append(`${filter.id}[]`, val)
        );
      } else {
        queryParams.append(filter.id, filter.value as string);
      }
    }
  });

  // Make API call (replace with your actual endpoint)
  const response = await fetch(`/api/tasks?${queryParams.toString()}`);

  if (!response.ok) {
    throw new Error("Failed to fetch tasks");
  }

  const result = await response.json();

  return {
    data: result.data,
    pageCount: result.totalPages,
  };
}

export default function ServerSideTaskPage() {
  const [data, setData] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [pageCount, setPageCount] = React.useState(-1);

  // Table state
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 25,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );

  // Fetch data when state changes
  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchTasks({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch tasks:", error);
        // Handle error state here
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <>
      <div className="hidden h-full flex-1 flex-col gap-8 p-8 md:flex">
        <div className="flex items-center justify-between gap-2">
          <div className="flex flex-col gap-1">
            <h2 className="text-2xl font-semibold tracking-tight">
              Welcome back!
            </h2>
            <p className="text-muted-foreground">
              Here&apos;s a list of your tasks for this month.
            </p>
          </div>
        </div>

        <DataTable
          data={data}
          columns={columns}
          loading={loading}
          pageCount={pageCount}
          onPaginationChange={setPagination}
          onSortingChange={setSorting}
          onColumnFiltersChange={setColumnFilters}
          manualPagination={true}
          manualSorting={true}
          manualFiltering={true}
          filterOptions={{
            statuses: statusOptions,
            priorities: priorityOptions,
          }}
          searchColumn="title"
          searchPlaceholder="Filter tasks..."
          enableBulkActions={true}
          bulkActions={studentBulkActions}
        />
      </div>
    </>
  );
}
