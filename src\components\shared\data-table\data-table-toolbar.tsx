"use client";

import { Table } from "@tanstack/react-table";
import { X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableViewOptions } from "./data-table-view-options";

import { DataTableFacetedFilter } from "./data-table-faceted-filter";

// You'll need to import these from your data file or pass them as props
// import { priorities, statuses } from "../data/data";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filterOptions?: {
    statuses?: {
      label: string;
      value: string;
      icon?: React.ComponentType<{ className?: string }>;
    }[];
    priorities?: {
      label: string;
      value: string;
      icon?: React.ComponentType<{ className?: string }>;
    }[];
  };
  searchColumn?: string;
  searchPlaceholder?: string;
}

export function DataTableToolbar<TData>({
  table,
  filterOptions,
  searchColumn = "title",
  searchPlaceholder = "Filter...",
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center gap-2">
        <Input
          placeholder={searchPlaceholder}
          value={
            (table.getColumn(searchColumn)?.getFilterValue() as string) ?? ""
          }
          onChange={(event) =>
            table.getColumn(searchColumn)?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {table.getColumn("status") && filterOptions?.statuses && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={filterOptions.statuses}
          />
        )}
        {table.getColumn("priority") && filterOptions?.priorities && (
          <DataTableFacetedFilter
            column={table.getColumn("priority")}
            title="Priority"
            options={filterOptions.priorities}
          />
        )}
        {isFiltered && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.resetColumnFilters()}
          >
            Reset
            <X />
          </Button>
        )}
      </div>
      <div className="flex items-center gap-2">
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
