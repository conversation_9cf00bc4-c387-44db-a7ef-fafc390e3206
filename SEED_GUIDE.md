# Database Seeding Guide

## 🌱 Overview

The seed file creates a comprehensive dataset for the Kingdom SIS with:
- **1 School** (Kingdom International School)
- **8 Departments** (CS, Math, Physics, Chemistry, Biology, Engineering, Business, Arts)
- **250+ Students** with realistic data distribution
- **3 Sample Teachers**
- **1 Admin User**

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Install faker for generating realistic test data
npm install --save-dev @faker-js/faker ts-node
```

### 2. Run the Seed
```bash
# Make sure your database is running
npm run db:up

# Push the schema to create tables
npm run db:push

# Run the seed script
npm run db:seed
```

## 📊 Generated Data

### Students (250 records)
- **Realistic Names**: Generated using Faker.js
- **Email Addresses**: Based on first/last names
- **Student IDs**: Format `KIS{year}{0001-0250}`
- **Status Distribution**:
  - 85% Active
  - 8% Inactive  
  - 4% Suspended
  - 3% Graduated
- **Enrollment Types**:
  - 75% Full-time
  - 15% Part-time
  - 8% Online
  - 2% Hybrid
- **Academic Years**: 1st, 2nd, 3rd, 4th year students
- **Grades**: A, B, C, D, F with realistic GPA correlation
- **Departments**: Evenly distributed across all 8 departments

### Realistic Data Features
- **Enrollment Dates**: Based on academic year (4th years enrolled 4 years ago)
- **GPA Correlation**: Higher GPAs for better grades
- **Email Verification**: 85% of users have verified emails
- **Graduation Dates**: Only for graduated students
- **Avatar URLs**: Generated profile pictures

## 🔧 Customization

### Modify Student Count
```typescript
// In prisma/seed.ts, line 85
const studentsToCreate = 250; // Change this number
```

### Add More Departments
```typescript
// In prisma/seed.ts, add to departments array
const departments = [
  // ... existing departments
  { name: 'Psychology', code: 'PSYC', description: 'Psychology and Behavioral Sciences' },
  { name: 'History', code: 'HIST', description: 'History and Social Studies' },
];
```

### Adjust Status Distribution
```typescript
// In prisma/seed.ts, modify the weightedArrayElement
const status = faker.helpers.weightedArrayElement([
  { weight: 90, value: 'active' as StudentStatus },    // 90% active
  { weight: 5, value: 'inactive' as StudentStatus },   // 5% inactive
  { weight: 3, value: 'suspended' as StudentStatus },  // 3% suspended
  { weight: 2, value: 'graduated' as StudentStatus },  // 2% graduated
]);
```

## 📋 Sample Queries

After seeding, you can test with these queries:

### Check Total Counts
```sql
-- Total students by status
SELECT status, COUNT(*) as count 
FROM kingdom_sis.students 
GROUP BY status;

-- Students by department
SELECT d.name, COUNT(s.id) as student_count
FROM kingdom_sis.departments d
LEFT JOIN kingdom_sis.students s ON d.id = s.department_id
GROUP BY d.name
ORDER BY student_count DESC;

-- Students by year
SELECT year, COUNT(*) as count
FROM kingdom_sis.students
GROUP BY year
ORDER BY year;
```

### Sample Student Data
```sql
-- Get 10 random students with their details
SELECT 
  u.name,
  s.student_id,
  s.status,
  s.grade,
  s.year,
  d.name as department,
  s.gpa
FROM kingdom_sis.students s
JOIN kingdom_sis.users u ON s.user_id = u.id
JOIN kingdom_sis.departments d ON s.department_id = d.id
ORDER BY RANDOM()
LIMIT 10;
```

## 🔄 Re-seeding

To clear and re-seed the database:

```bash
# Option 1: Reset database completely
npm run db:reset
npm run db:push
npm run db:seed

# Option 2: Just re-run seed (it cleans existing data first)
npm run db:seed
```

## 🎯 Testing Your Data Table

After seeding, your data table should show:
- **250 students** with various filters working
- **Multiple departments** for department filtering
- **Different statuses** for status filtering
- **Various grades** for grade filtering
- **Different years** for year filtering
- **Mixed enrollment types** for enrollment filtering

## 📈 Performance Notes

- **Batch Processing**: Students are created in batches of 25 for optimal performance
- **Transactions**: Each batch uses database transactions for data integrity
- **Realistic Distribution**: Data follows realistic patterns for better testing
- **Memory Efficient**: Processes data in chunks to avoid memory issues

## 🛠️ Troubleshooting

### Seed Fails with "Table doesn't exist"
```bash
# Make sure to push schema first
npm run db:push
npm run db:seed
```

### Out of Memory Error
```bash
# Reduce batch size in seed.ts
const batchSize = 10; // Reduce from 25 to 10
```

### Duplicate Key Errors
```bash
# The seed script cleans existing data first, but if you get errors:
npm run db:reset
npm run db:push
npm run db:seed
```

### TypeScript Errors
```bash
# Make sure ts-node is installed
npm install --save-dev ts-node

# Generate Prisma client
npm run db:generate
```

## 🎉 Success!

After successful seeding, you should see output like:
```
✅ Seeding completed successfully!
📊 Created:
   🏫 Schools: 1
   🏢 Departments: 8
   👤 Users: 254
   👨‍🎓 Students: 250
```

Your database is now ready for testing with realistic data!
