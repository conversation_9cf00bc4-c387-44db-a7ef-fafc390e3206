import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// This is a mock implementation - replace with your actual database queries
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '25');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    const titleFilter = searchParams.get('title');
    const statusFilters = searchParams.getAll('status[]');
    const priorityFilters = searchParams.getAll('priority[]');

    // Load data (replace with your database query)
    const data = await fs.readFile(
      path.join(process.cwd(), "src/data/tasks.json")
    );
    let tasks = JSON.parse(data.toString());

    // Apply filters
    if (titleFilter) {
      tasks = tasks.filter((task: any) => 
        task.title.toLowerCase().includes(titleFilter.toLowerCase())
      );
    }

    if (statusFilters.length > 0) {
      tasks = tasks.filter((task: any) => 
        statusFilters.includes(task.status)
      );
    }

    if (priorityFilters.length > 0) {
      tasks = tasks.filter((task: any) => 
        priorityFilters.includes(task.priority)
      );
    }

    // Apply sorting
    if (sortBy) {
      tasks.sort((a: any, b: any) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        
        if (aValue < bValue) return sortOrder === 'desc' ? 1 : -1;
        if (aValue > bValue) return sortOrder === 'desc' ? -1 : 1;
        return 0;
      });
    }

    // Calculate pagination
    const total = tasks.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedTasks = tasks.slice(startIndex, endIndex);

    // Return response in expected format
    return NextResponse.json({
      data: paginatedTasks,
      total,
      totalPages,
      currentPage: page,
      pageSize,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    });

  } catch (error) {
    console.error('Error fetching tasks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    );
  }
}

// For more complex filtering, you might want to use POST
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { pagination, sorting, columnFilters } = body;

    // Load data (replace with your database query)
    const data = await fs.readFile(
      path.join(process.cwd(), "src/data/tasks.json")
    );
    let tasks = JSON.parse(data.toString());

    // Apply column filters
    columnFilters.forEach((filter: any) => {
      if (filter.value) {
        if (filter.id === 'title') {
          tasks = tasks.filter((task: any) => 
            task.title.toLowerCase().includes(filter.value.toLowerCase())
          );
        } else if (Array.isArray(filter.value)) {
          tasks = tasks.filter((task: any) => 
            filter.value.includes(task[filter.id])
          );
        } else {
          tasks = tasks.filter((task: any) => 
            task[filter.id] === filter.value
          );
        }
      }
    });

    // Apply sorting
    if (sorting.length > 0) {
      const sort = sorting[0];
      tasks.sort((a: any, b: any) => {
        const aValue = a[sort.id];
        const bValue = b[sort.id];
        
        if (aValue < bValue) return sort.desc ? 1 : -1;
        if (aValue > bValue) return sort.desc ? -1 : 1;
        return 0;
      });
    }

    // Apply pagination
    const { pageIndex, pageSize } = pagination;
    const total = tasks.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = pageIndex * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedTasks = tasks.slice(startIndex, endIndex);

    return NextResponse.json({
      data: paginatedTasks,
      pageCount: totalPages,
      total,
    });

  } catch (error) {
    console.error('Error fetching tasks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    );
  }
}
