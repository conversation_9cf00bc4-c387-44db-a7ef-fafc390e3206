"use client";

import * as React from "react";
import { ColumnDef, ColumnFiltersState, PaginationState, SortingState } from "@tanstack/react-table";
import { DataTable } from "./data-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "./data-table-column-header";
import { User, Shield, Calendar, GraduationCap } from "lucide-react";

// Example data type for a student management system
interface Student {
  id: string;
  name: string;
  email: string;
  status: string;
  grade: string;
  department: string;
  enrollmentType: string;
  year: number;
}

// Dynamic filter configurations - you can easily add/remove/modify these
const filterConfigs = [
  {
    columnId: "status",
    title: "Status",
    options: [
      { label: "Active", value: "active", icon: User },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended", icon: Shield },
      { label: "Graduated", value: "graduated", icon: GraduationCap },
    ],
  },
  {
    columnId: "grade",
    title: "Grade",
    options: [
      { label: "A", value: "A" },
      { label: "B", value: "B" },
      { label: "C", value: "C" },
      { label: "D", value: "D" },
      { label: "F", value: "F" },
    ],
  },
  {
    columnId: "department",
    title: "Department",
    options: [
      { label: "Computer Science", value: "cs" },
      { label: "Mathematics", value: "math" },
      { label: "Physics", value: "physics" },
      { label: "Chemistry", value: "chemistry" },
      { label: "Biology", value: "biology" },
      { label: "English", value: "english" },
    ],
  },
  {
    columnId: "enrollmentType",
    title: "Enrollment",
    options: [
      { label: "Full-time", value: "fulltime" },
      { label: "Part-time", value: "parttime" },
      { label: "Online", value: "online" },
      { label: "Hybrid", value: "hybrid" },
    ],
  },
  {
    columnId: "year",
    title: "Year",
    options: [
      { label: "Freshman", value: "1" },
      { label: "Sophomore", value: "2" },
      { label: "Junior", value: "3" },
      { label: "Senior", value: "4" },
      { label: "Graduate", value: "5" },
    ],
  },
];

// Columns definition
const columns: ColumnDef<Student>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const statusConfig = filterConfigs[0].options.find(opt => opt.value === status);
      return (
        <div className="flex items-center gap-2">
          {statusConfig?.icon && <statusConfig.icon className="h-4 w-4" />}
          <Badge variant="outline">{statusConfig?.label || status}</Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "grade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Grade" />
    ),
    cell: ({ row }) => {
      const grade = row.getValue("grade") as string;
      return <Badge variant="secondary">{grade}</Badge>;
    },
  },
  {
    accessorKey: "department",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Department" />
    ),
    cell: ({ row }) => {
      const dept = row.getValue("department") as string;
      const deptConfig = filterConfigs[2].options.find(opt => opt.value === dept);
      return <span>{deptConfig?.label || dept}</span>;
    },
  },
  {
    accessorKey: "enrollmentType",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Enrollment" />
    ),
  },
  {
    accessorKey: "year",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Year" />
    ),
    cell: ({ row }) => {
      const year = row.getValue("year") as string;
      const yearConfig = filterConfigs[4].options.find(opt => opt.value === year);
      return <span>{yearConfig?.label || `Year ${year}`}</span>;
    },
  },
];

// Mock API function
async function fetchStudents(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: Student[]; pageCount: number }> {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock data
  const allStudents: Student[] = Array.from({ length: 200 }, (_, i) => ({
    id: `student-${i + 1}`,
    name: `Student ${i + 1}`,
    email: `student${i + 1}@university.edu`,
    status: ["active", "inactive", "suspended", "graduated"][i % 4],
    grade: ["A", "B", "C", "D", "F"][i % 5],
    department: ["cs", "math", "physics", "chemistry", "biology", "english"][i % 6],
    enrollmentType: ["fulltime", "parttime", "online", "hybrid"][i % 4],
    year: (i % 5) + 1,
  }));

  // Apply filters and pagination (normally done on server)
  let filteredStudents = allStudents;
  
  params.columnFilters.forEach(filter => {
    if (filter.value) {
      if (filter.id === "name" && typeof filter.value === "string") {
        filteredStudents = filteredStudents.filter(student => 
          student.name.toLowerCase().includes(filter.value.toLowerCase())
        );
      } else if (Array.isArray(filter.value) && filter.value.length > 0) {
        filteredStudents = filteredStudents.filter(student => 
          filter.value.includes(String(student[filter.id as keyof Student]))
        );
      }
    }
  });

  const { pageIndex, pageSize } = params.pagination;
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedStudents = filteredStudents.slice(start, end);

  return {
    data: paginatedStudents,
    pageCount: Math.ceil(filteredStudents.length / pageSize),
  };
}

export function DynamicFilteringExample() {
  const [data, setData] = React.useState<Student[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pageCount, setPageCount] = React.useState(-1);
  
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchStudents({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Dynamic Filtering Example</h1>
        <p className="text-muted-foreground">
          This example shows how to configure multiple dynamic filters for any columns.
        </p>
      </div>
      
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search students by name..."
      />
    </div>
  );
}
