"use client";

import * as React from "react";
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { DataTable } from "@/components/shared/data-table/data-table";
import { columns } from "./columns";
import { filterConfigs } from "./filtering";
import { studentBulkActions } from "./bulk-actions";

// Example data type for a student management system
interface Student {
  id: string;
  name: string;
  email: string;
  status: string;
  grade: string;
  department: string;
  enrollmentType: string;
  year: number;
}

// Mock API function
async function fetchStudents(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: Student[]; pageCount: number }> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Mock data
  const allStudents: Student[] = Array.from({ length: 200 }, (_, i) => ({
    id: `student-${i + 1}`,
    name: `Student ${i + 1}`,
    email: `student${i + 1}@university.edu`,
    status: ["active", "inactive", "suspended", "graduated"][i % 4],
    grade: ["A", "B", "C", "D", "F"][i % 5],
    department: ["cs", "math", "physics", "chemistry", "biology", "english"][
      i % 6
    ],
    enrollmentType: ["fulltime", "parttime", "online", "hybrid"][i % 4],
    year: (i % 5) + 1,
  }));

  // Apply filters and pagination (normally done on server)
  let filteredStudents = allStudents;

  params.columnFilters.forEach((filter) => {
    if (filter.value) {
      if (filter.id === "name" && typeof filter.value === "string") {
        filteredStudents = filteredStudents.filter((student) =>
          student.name
            .toLowerCase()
            .includes((filter.value as string).toLowerCase())
        );
      } else if (Array.isArray(filter.value) && filter.value.length > 0) {
        filteredStudents = filteredStudents.filter((student) =>
          (filter.value as string[]).includes(
            String(student[filter.id as keyof Student])
          )
        );
      }
    }
  });

  const { pageIndex, pageSize } = params.pagination;
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedStudents = filteredStudents.slice(start, end);

  return {
    data: paginatedStudents,
    pageCount: Math.ceil(filteredStudents.length / pageSize),
  };
}

export default function DynamicFilteringExample() {
  const [data, setData] = React.useState<Student[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pageCount, setPageCount] = React.useState(-1);

  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );

  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchStudents({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Dynamic Filtering Example</h1>
        <p className="text-muted-foreground">
          This example shows how to configure multiple dynamic filters for any
          columns.
        </p>
      </div>

      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search students by name..."
        enableBulkActions={true}
        bulkActions={studentBulkActions}
      />
    </div>
  );
}
