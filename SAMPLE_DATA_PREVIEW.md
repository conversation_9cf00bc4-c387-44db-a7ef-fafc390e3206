# Sample Data Preview

## 📊 What the Seed Creates

### 🏫 School
```
Kingdom International School (KIS)
📍 123 Education Street, Knowledge City, KC 12345
📞 +1-555-0123
📧 <EMAIL>
🌐 https://kingdom.edu
```

### 🏢 Departments (8 total)
| Code | Name | Description |
|------|------|-------------|
| CS | Computer Science | Computer Science and Information Technology |
| MATH | Mathematics | Pure and Applied Mathematics |
| PHYS | Physics | Physics and Astronomy |
| CHEM | Chemistry | Chemistry and Biochemistry |
| BIO | Biology | Biology and Life Sciences |
| ENG | Engineering | Engineering and Technology |
| BUS | Business | Business Administration and Management |
| ARTS | Arts | Fine Arts and Creative Studies |

### 👨‍🎓 Sample Students (250 total)

#### Student Examples:
```
1. <PERSON> (KIS20001) - Computer Science - Active - GPA: 3.85 - Grade: A
2. <PERSON> (KIS30045) - Mathematics - Active - GPA: 3.42 - Grade: B
3. <PERSON> (KIS10123) - Physics - Inactive - GPA: 2.78 - Grade: C
4. <PERSON> (KIS40089) - Engineering - Active - GPA: 3.91 - Grade: A
5. <PERSON> (KIS20156) - Biology - Active - GPA: 3.23 - Grade: B
```

#### Status Distribution:
- **Active**: ~212 students (85%)
- **Inactive**: ~20 students (8%)
- **Suspended**: ~10 students (4%)
- **Graduated**: ~8 students (3%)

#### Year Distribution:
- **1st Year**: ~62 students (25%)
- **2nd Year**: ~63 students (25%)
- **3rd Year**: ~62 students (25%)
- **4th Year**: ~63 students (25%)

#### Enrollment Type Distribution:
- **Full-time**: ~187 students (75%)
- **Part-time**: ~37 students (15%)
- **Online**: ~20 students (8%)
- **Hybrid**: ~6 students (2%)

#### Grade Distribution (with realistic GPA correlation):
- **Grade A**: GPA 3.7-4.0
- **Grade B**: GPA 3.0-3.6
- **Grade C**: GPA 2.3-2.9
- **Grade D**: GPA 2.0-2.2
- **Grade F**: GPA 0.0-1.9

### 👨‍🏫 Sample Teachers (3 total)
```
1. Dr. John Smith (<EMAIL>) - Teacher
2. Prof. Jane Doe (<EMAIL>) - Teacher  
3. Dr. Mike Wilson (<EMAIL>) - Teacher
```

### 👤 Admin User
```
System Administrator (<EMAIL>) - Admin Role
```

## 🎯 Perfect for Testing Your Data Table

This seed data provides excellent test coverage for:

### ✅ Filtering Features
- **Status Filter**: Mix of active, inactive, suspended, graduated
- **Grade Filter**: All grades A-F represented
- **Department Filter**: 8 different departments
- **Year Filter**: Students from all 4 years
- **Enrollment Type Filter**: All 4 enrollment types

### ✅ Sorting Features
- **Name Sorting**: Diverse first/last names
- **GPA Sorting**: Range from 0.0 to 4.0
- **Date Sorting**: Various enrollment dates
- **ID Sorting**: Sequential student IDs

### ✅ Search Features
- **Name Search**: Realistic first/last name combinations
- **Email Search**: Consistent email format
- **Student ID Search**: Structured ID format (KIS{year}{number})

### ✅ Pagination Testing
- **250 records** perfect for testing pagination
- **Multiple pages** at various page sizes (10, 25, 50, 100)
- **Performance testing** with realistic data volume

## 📈 Data Quality Features

### Realistic Relationships
- **Enrollment dates** correlate with academic year
- **GPA values** correlate with letter grades
- **Email addresses** match student names
- **Student IDs** follow institutional format

### Data Integrity
- **Foreign key relationships** properly maintained
- **Enum constraints** respected
- **Date logic** (enrollment before graduation)
- **Status consistency** (graduated students have graduation dates)

### Diversity & Realism
- **Name diversity** using Faker.js international names
- **Realistic distributions** based on typical university demographics
- **Edge cases** included (suspended students, low GPAs, etc.)
- **Email verification** status varies realistically

## 🔍 Sample Queries You Can Test

### Filter Combinations
```sql
-- Active Computer Science students with GPA > 3.5
SELECT * FROM students s
JOIN users u ON s.user_id = u.id
JOIN departments d ON s.department_id = d.id
WHERE s.status = 'active' 
  AND d.code = 'CS' 
  AND s.gpa > 3.5;

-- Part-time students in their final year
SELECT * FROM students 
WHERE enrollment_type = 'parttime' 
  AND year = 4;

-- Students enrolled in 2023
SELECT * FROM students 
WHERE EXTRACT(YEAR FROM enrollment_date) = 2023;
```

### Aggregations
```sql
-- Average GPA by department
SELECT d.name, AVG(s.gpa) as avg_gpa
FROM students s
JOIN departments d ON s.department_id = d.id
GROUP BY d.name
ORDER BY avg_gpa DESC;

-- Student count by status and year
SELECT status, year, COUNT(*) as count
FROM students
GROUP BY status, year
ORDER BY year, status;
```

## 🎨 Visual Data Distribution

### Status Pie Chart
```
🟢 Active (85%)     ████████████████████████████████████████████
🟡 Inactive (8%)    ████████
🔴 Suspended (4%)   ████
🎓 Graduated (3%)   ███
```

### Department Bar Chart
```
CS       ████████████████████████████████ (31-32 students)
MATH     ████████████████████████████████ (31-32 students)
PHYS     ████████████████████████████████ (31-32 students)
CHEM     ████████████████████████████████ (31-32 students)
BIO      ████████████████████████████████ (31-32 students)
ENG      ████████████████████████████████ (31-32 students)
BUS      ████████████████████████████████ (31-32 students)
ARTS     ████████████████████████████████ (31-32 students)
```

## 🚀 Ready to Test!

After running the seed, your data table will have:
- ✅ **Rich, realistic data** for comprehensive testing
- ✅ **Multiple filter combinations** possible
- ✅ **Performance testing** with 250+ records
- ✅ **Edge cases** for robust validation
- ✅ **Consistent relationships** for data integrity testing

Perfect for testing your server-side filtering, sorting, pagination, and bulk actions!
