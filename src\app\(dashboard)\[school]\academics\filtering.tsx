import {
  Trash2,
  Edit,
  Mail,
  Download,
  Archive,
  UserCheck,
  UserX,
  GraduationCap,
  Shield,
  User,
} from "lucide-react";
import { toast } from "sonner";

interface Student {
  id: string;
  name: string;
  email: string;
  status: string;
  grade: string;
  department: string;
  enrollmentType: string;
  year: number;
}

export const filterConfigs = [
  {
    columnId: "status",
    title: "Status",
    options: [
      { label: "Active", value: "active", icon: User },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended", icon: Shield },
      { label: "Graduated", value: "graduated", icon: GraduationCap },
    ],
  },
  {
    columnId: "grade",
    title: "Grade",
    options: [
      { label: "A", value: "A" },
      { label: "B", value: "B" },
      { label: "C", value: "C" },
      { label: "D", value: "D" },
      { label: "F", value: "F" },
    ],
  },
  {
    columnId: "department",
    title: "Department",
    options: [
      { label: "Computer Science", value: "cs" },
      { label: "Mathematics", value: "math" },
      { label: "Physics", value: "physics" },
      { label: "Chemistry", value: "chemistry" },
      { label: "Biology", value: "biology" },
      { label: "English", value: "english" },
    ],
  },
  {
    columnId: "enrollmentType",
    title: "Enrollment",
    options: [
      { label: "Full-time", value: "fulltime" },
      { label: "Part-time", value: "parttime" },
      { label: "Online", value: "online" },
      { label: "Hybrid", value: "hybrid" },
    ],
  },
  {
    columnId: "year",
    title: "Year",
    options: [
      { label: "Freshman", value: "1" },
      { label: "Sophomore", value: "2" },
      { label: "Junior", value: "3" },
      { label: "Senior", value: "4" },
      { label: "Graduate", value: "5" },
    ],
  },
];
