# Server-Side Filtering Migration Guide

This guide explains how to migrate your TanStack data-table from client-side filtering to server-side filtering.

## What Changed

### 1. Updated DataTable Component

The `DataTable` component now supports server-side operations with these new props:

```typescript
interface DataTableProps<TData, TValue> {
  // ... existing props
  
  // Server-side props
  loading?: boolean;
  pageCount?: number;
  onPaginationChange?: (pagination: PaginationState) => void;
  onSortingChange?: (sorting: SortingState) => void;
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
  
  // Manual control flags
  manualPagination?: boolean;
  manualSorting?: boolean;
  manualFiltering?: boolean;
  
  // Toolbar configuration
  filterOptions?: {
    statuses?: FilterOption[];
    priorities?: FilterOption[];
  };
  searchColumn?: string;
  searchPlaceholder?: string;
}
```

### 2. Removed Client-Side Models

The following TanStack models were removed for server-side operation:
- `getFilteredRowModel()` - Filtering now handled by server
- `getPaginationRowModel()` - Pagination now handled by server  
- `getFacetedRowModel()` - Faceted filtering now handled by server
- `getFacetedUniqueValues()` - Unique values now handled by server

### 3. Updated Toolbar

The `DataTableToolbar` now accepts filter options as props instead of importing them statically.

## Migration Steps

### Step 1: Update Your Data Fetching

Replace static data with a server-side fetch function:

```typescript
// Before (Client-side)
const data = staticTaskData;

// After (Server-side)
async function fetchTasks(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: Task[]; pageCount: number }> {
  const response = await fetch('/api/tasks', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
  });
  return response.json();
}
```

### Step 2: Add State Management

Add state for server-side operations:

```typescript
const [data, setData] = React.useState<Task[]>([]);
const [loading, setLoading] = React.useState(true);
const [pageCount, setPageCount] = React.useState(-1);

const [pagination, setPagination] = React.useState<PaginationState>({
  pageIndex: 0,
  pageSize: 10,
});
const [sorting, setSorting] = React.useState<SortingState>([]);
const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
```

### Step 3: Add Data Loading Effect

```typescript
React.useEffect(() => {
  const loadData = async () => {
    setLoading(true);
    try {
      const result = await fetchTasks({
        pagination,
        sorting,
        columnFilters,
      });
      setData(result.data);
      setPageCount(result.pageCount);
    } catch (error) {
      console.error("Failed to fetch data:", error);
    } finally {
      setLoading(false);
    }
  };

  loadData();
}, [pagination, sorting, columnFilters]);
```

### Step 4: Update DataTable Usage

```typescript
// Before (Client-side)
<DataTable columns={columns} data={staticData} />

// After (Server-side)
<DataTable
  columns={columns}
  data={data}
  loading={loading}
  pageCount={pageCount}
  onPaginationChange={setPagination}
  onSortingChange={setSorting}
  onColumnFiltersChange={setColumnFilters}
  manualPagination={true}
  manualSorting={true}
  manualFiltering={true}
  filterOptions={{
    statuses: statusOptions,
    priorities: priorityOptions,
  }}
  searchColumn="title"
  searchPlaceholder="Search tasks..."
/>
```

## Server-Side API Requirements

Your server endpoint should handle these parameters:

```typescript
interface ServerParams {
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  sorting: Array<{
    id: string;
    desc: boolean;
  }>;
  columnFilters: Array<{
    id: string;
    value: string | string[];
  }>;
}
```

And return this format:

```typescript
interface ServerResponse<T> {
  data: T[];
  pageCount: number;
  total?: number; // Optional: total number of records
}
```

## Example Implementation

See `src/components/shared/data-table/server-side-example.tsx` for a complete working example.

## Benefits of Server-Side Filtering

1. **Performance**: Handle large datasets without loading all data to client
2. **Memory Efficiency**: Only load current page data
3. **Real-time Data**: Always fetch fresh data from server
4. **Advanced Filtering**: Support complex server-side queries
5. **Scalability**: Works with millions of records

## Backward Compatibility

The component still supports client-side filtering by:
- Not setting the `manual*` flags to `true`
- Not providing the callback functions
- Using the component as before

This allows gradual migration of existing implementations.
