"use client";

import * as React from "react";
import { ColumnDef, ColumnFiltersState, PaginationState, SortingState } from "@tanstack/react-table";
import { DataTable } from "./data-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "./data-table-column-header";

// Example data type
interface Task {
  id: string;
  title: string;
  status: string;
  priority: string;
}

// Example filter options
const statusOptions = [
  { label: "Todo", value: "todo" },
  { label: "In Progress", value: "in-progress" },
  { label: "Done", value: "done" },
];

const priorityOptions = [
  { label: "Low", value: "low" },
  { label: "Medium", value: "medium" },
  { label: "High", value: "high" },
];

// Example columns definition
const columns: ColumnDef<Task>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return <Badge variant="outline">{status}</Badge>;
    },
  },
  {
    accessorKey: "priority",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Priority" />
    ),
    cell: ({ row }) => {
      const priority = row.getValue("priority") as string;
      return <Badge variant="secondary">{priority}</Badge>;
    },
  },
];

// Mock API function - replace with your actual API call
async function fetchTasks(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: Task[]; pageCount: number }> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock data - replace with actual API call
  const allTasks: Task[] = Array.from({ length: 100 }, (_, i) => ({
    id: `task-${i + 1}`,
    title: `Task ${i + 1}`,
    status: ["todo", "in-progress", "done"][i % 3],
    priority: ["low", "medium", "high"][i % 3],
  }));

  // Apply filters (in real implementation, this would be done on the server)
  let filteredTasks = allTasks;
  
  // Apply column filters
  params.columnFilters.forEach(filter => {
    if (filter.id === "title" && filter.value) {
      filteredTasks = filteredTasks.filter(task => 
        task.title.toLowerCase().includes((filter.value as string).toLowerCase())
      );
    }
    if (filter.id === "status" && Array.isArray(filter.value) && filter.value.length > 0) {
      filteredTasks = filteredTasks.filter(task => 
        (filter.value as string[]).includes(task.status)
      );
    }
    if (filter.id === "priority" && Array.isArray(filter.value) && filter.value.length > 0) {
      filteredTasks = filteredTasks.filter(task => 
        (filter.value as string[]).includes(task.priority)
      );
    }
  });

  // Apply sorting
  if (params.sorting.length > 0) {
    const sort = params.sorting[0];
    filteredTasks.sort((a, b) => {
      const aValue = a[sort.id as keyof Task];
      const bValue = b[sort.id as keyof Task];
      if (aValue < bValue) return sort.desc ? 1 : -1;
      if (aValue > bValue) return sort.desc ? -1 : 1;
      return 0;
    });
  }

  // Apply pagination
  const { pageIndex, pageSize } = params.pagination;
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedTasks = filteredTasks.slice(start, end);

  return {
    data: paginatedTasks,
    pageCount: Math.ceil(filteredTasks.length / pageSize),
  };
}

export function ServerSideDataTableExample() {
  const [data, setData] = React.useState<Task[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pageCount, setPageCount] = React.useState(-1);
  
  // Table state
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  // Fetch data when state changes
  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchTasks({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <div className="container mx-auto py-10">
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterOptions={{
          statuses: statusOptions,
          priorities: priorityOptions,
        }}
        searchColumn="title"
        searchPlaceholder="Search tasks..."
      />
    </div>
  );
}
