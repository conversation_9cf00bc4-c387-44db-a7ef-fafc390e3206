# Quick Start Guide - PostgreSQL 17 + Kingdom SIS

## 🚀 Quick Setup (5 minutes)

### 1. Start PostgreSQL Database
```bash
# Start PostgreSQL with Docker
npm run db:up

# Check if it's running
npm run db:logs
```

### 2. Set up Prisma
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (creates tables)
npm run db:push
```

### 3. Test Database Connection
```bash
# Start your Next.js app
npm run dev

# Test database health (in another terminal)
npm run db:health
```

Visit: http://localhost:3000/api/health/database

## 📋 What's Included

### ✅ Docker Services
- **PostgreSQL 17** - Main database (port 5432)
- **pgAdmin 4** - Database management UI (port 5050)
- **Redis** - Caching (port 6379) [Optional]

### ✅ Database Schema
- **Users** - Authentication and user management
- **Schools** - Multi-school support
- **Departments** - School departments
- **Students** - Student information and enrollment
- **Enums** - UserRole, StudentStatus, EnrollmentType

### ✅ Environment Variables
```env
DATABASE_URL="postgresql://kingdom_user:kingdom_password_2024@localhost:5432/kingdom_sis"
POSTGRES_DB=kingdom_sis
POSTGRES_USER=kingdom_user
POSTGRES_PASSWORD=kingdom_password_2024
```

## 🛠️ Available Commands

### Database Management
```bash
npm run db:up          # Start PostgreSQL
npm run db:down        # Stop all services
npm run db:logs        # View PostgreSQL logs
npm run db:reset       # Reset database (WARNING: deletes all data)
npm run db:health      # Test database connection
```

### Prisma Commands
```bash
npm run db:generate    # Generate Prisma client
npm run db:push        # Push schema to database
npm run db:migrate     # Create and run migrations
npm run db:studio      # Open Prisma Studio
npm run db:seed        # Run database seeds
```

### Backup & Restore
```bash
npm run db:backup      # Create database backup
# Restore: docker exec -i kingdom_sis_postgres psql -U kingdom_user -d kingdom_sis < backup.sql
```

## 🔧 Access Database

### Option 1: pgAdmin (Web UI)
1. Open http://localhost:5050
2. Login: `<EMAIL>` / `admin123`
3. Add server:
   - Host: `postgres` (or `localhost` from outside Docker)
   - Port: `5432`
   - Database: `kingdom_sis`
   - Username: `kingdom_user`
   - Password: `kingdom_password_2024`

### Option 2: Command Line
```bash
# Connect via Docker
docker exec -it kingdom_sis_postgres psql -U kingdom_user -d kingdom_sis

# Or use any PostgreSQL client with:
# Host: localhost, Port: 5432, Database: kingdom_sis
# Username: kingdom_user, Password: kingdom_password_2024
```

### Option 3: Prisma Studio
```bash
npm run db:studio
# Opens http://localhost:5555
```

## 📊 Database Structure

```sql
-- Check schemas
\dn

-- List tables in kingdom_sis schema
\dt kingdom_sis.*

-- Sample queries
SELECT * FROM kingdom_sis.users;
SELECT * FROM kingdom_sis.students;
SELECT * FROM kingdom_sis.schools;
```

## 🔍 Troubleshooting

### Database won't start?
```bash
# Check Docker status
docker ps

# Check logs
npm run db:logs

# Reset everything
npm run db:reset
```

### Connection issues?
1. Verify `.env` file has correct `DATABASE_URL`
2. Check if PostgreSQL is running: `npm run db:logs`
3. Test connection: `npm run db:health`

### Port conflicts?
Edit `docker-compose.yml` to change ports:
```yaml
ports:
  - "5433:5432"  # Change from 5432 to 5433
```

## 🎯 Next Steps

1. **Start developing**: Your database is ready!
2. **Add sample data**: Create seed files in `prisma/seed.ts`
3. **Create API routes**: Use Prisma client in your API routes
4. **Set up authentication**: Integrate with NextAuth.js
5. **Deploy**: Configure for production environment

## 📚 Useful Resources

- [Prisma Documentation](https://www.prisma.io/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

---

**🎉 You're all set!** Your PostgreSQL 17 database is running and ready for development.
