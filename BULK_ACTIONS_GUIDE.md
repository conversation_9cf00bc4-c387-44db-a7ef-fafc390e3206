# Bulk Actions Implementation Guide

This guide shows you how to implement bulk actions in your DataTable component.

## Overview

Bulk actions allow users to perform operations on multiple selected rows at once, such as:
- Delete multiple records
- Update status for multiple items
- Export selected data
- Send notifications to multiple users
- Archive/activate multiple records

## Basic Implementation

### 1. Enable Bulk Actions

First, enable bulk actions in your DataTable:

```typescript
<DataTable
  columns={columns}
  data={data}
  enableBulkActions={true}
  bulkActions={bulkActions}
  // ... other props
/>
```

### 2. Add Selection Column

Make sure your columns include a selection column:

```typescript
const columns: ColumnDef<YourDataType>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // ... your other columns
];
```

### 3. Define Bulk Actions

Create your bulk actions configuration:

```typescript
const bulkActions = [
  {
    key: "delete",
    label: "Delete Selected",
    icon: <Trash2 className="h-4 w-4" />,
    variant: "destructive" as const,
    onClick: (selectedRows: YourDataType[]) => {
      // Handle bulk delete
      console.log("Deleting:", selectedRows);
    },
  },
  {
    key: "export",
    label: "Export to CSV",
    icon: <Download className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: YourDataType[]) => {
      // Handle export
      exportToCSV(selectedRows);
    },
  },
];
```

## Bulk Action Configuration

Each bulk action supports these properties:

- **`key`**: Unique identifier for the action
- **`label`**: Display text for the button
- **`icon`**: Optional React component (e.g., Lucide icons)
- **`variant`**: Button style variant
  - `"default"` - Primary blue button
  - `"destructive"` - Red button for dangerous actions
  - `"outline"` - Outlined button
  - `"secondary"` - Gray button
  - `"ghost"` - Transparent button
  - `"link"` - Link-style button
- **`onClick`**: Function that receives the selected rows

## Real-World Examples

### Student Management System

```typescript
import { studentBulkActions } from "./bulk-actions";

<DataTable
  columns={studentColumns}
  data={students}
  enableBulkActions={true}
  bulkActions={studentBulkActions}
  filterConfigs={filterConfigs}
  // ... other props
/>
```

### E-commerce Orders

```typescript
const orderBulkActions = [
  {
    key: "mark-shipped",
    label: "Mark as Shipped",
    icon: <Truck className="h-4 w-4" />,
    variant: "default",
    onClick: async (selectedRows) => {
      await updateOrderStatus(selectedRows.map(r => r.id), "shipped");
      toast.success(`Marked ${selectedRows.length} orders as shipped`);
    },
  },
  {
    key: "cancel-orders",
    label: "Cancel Orders",
    icon: <X className="h-4 w-4" />,
    variant: "destructive",
    onClick: async (selectedRows) => {
      if (confirm(`Cancel ${selectedRows.length} orders?`)) {
        await cancelOrders(selectedRows.map(r => r.id));
        toast.success(`Cancelled ${selectedRows.length} orders`);
      }
    },
  },
];
```

### Project Management

```typescript
const projectBulkActions = [
  {
    key: "archive",
    label: "Archive Projects",
    icon: <Archive className="h-4 w-4" />,
    variant: "outline",
    onClick: async (selectedRows) => {
      await archiveProjects(selectedRows.map(r => r.id));
      toast.info(`Archived ${selectedRows.length} projects`);
    },
  },
  {
    key: "assign-team",
    label: "Assign Team",
    icon: <Users className="h-4 w-4" />,
    variant: "secondary",
    onClick: (selectedRows) => {
      openTeamAssignmentModal(selectedRows);
    },
  },
];
```

## Advanced Features

### 1. Conditional Actions

Show different actions based on selection:

```typescript
const getDynamicBulkActions = (selectedRows: Student[]) => {
  const actions = [];
  
  // Only show graduate action if all selected are seniors
  if (selectedRows.every(student => student.year === 4)) {
    actions.push({
      key: "graduate",
      label: "Mark as Graduated",
      icon: <GraduationCap className="h-4 w-4" />,
      variant: "default",
      onClick: (rows) => graduateStudents(rows),
    });
  }
  
  // Always show delete
  actions.push({
    key: "delete",
    label: "Delete Selected",
    icon: <Trash2 className="h-4 w-4" />,
    variant: "destructive",
    onClick: (rows) => deleteStudents(rows),
  });
  
  return actions;
};
```

### 2. Server-Side Bulk Operations

```typescript
const bulkActions = [
  {
    key: "bulk-update",
    label: "Update Status",
    icon: <Edit className="h-4 w-4" />,
    variant: "default",
    onClick: async (selectedRows) => {
      try {
        const response = await fetch('/api/students/bulk-update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            studentIds: selectedRows.map(row => row.id),
            updates: { status: 'active' }
          }),
        });
        
        if (response.ok) {
          toast.success(`Updated ${selectedRows.length} students`);
          // Refresh data
          refetchData();
        }
      } catch (error) {
        toast.error('Failed to update students');
      }
    },
  },
];
```

### 3. Export Functionality

```typescript
const exportToCSV = (data: any[], filename: string = 'export.csv') => {
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => `"${row[header]}"`).join(',')
    )
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.click();
  window.URL.revokeObjectURL(url);
};
```

## Best Practices

1. **Confirmation for Destructive Actions**: Always confirm before deleting or making irreversible changes
2. **Loading States**: Show loading indicators during bulk operations
3. **Error Handling**: Provide clear error messages if operations fail
4. **Progress Feedback**: Use toast notifications to show operation results
5. **Batch Size Limits**: Consider limiting the number of items that can be selected at once
6. **Permissions**: Check user permissions before showing certain bulk actions

## Complete Example

See `src/components/shared/data-table/bulk-actions-example.tsx` for a complete working example with multiple bulk actions.

## Integration with Your Existing Code

To add bulk actions to your current filtering setup:

1. Import the bulk actions: `import { studentBulkActions } from "./bulk-actions"`
2. Add to your DataTable: `enableBulkActions={true}` and `bulkActions={studentBulkActions}`
3. Ensure your columns include the selection column
4. Customize the actions in `bulk-actions.ts` for your specific needs
