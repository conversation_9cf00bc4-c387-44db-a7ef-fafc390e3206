# Dynamic Filtering Examples

## Overview

The updated DataTable now supports dynamic filtering configuration, allowing you to set up filters for any columns without hardcoding them in the toolbar component.

## Basic Usage

### 1. Simple Text-based Filters

```typescript
const filterConfigs = [
  {
    columnId: "status",
    title: "Status",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Pending", value: "pending" },
    ],
  },
];

<DataTable
  columns={columns}
  data={data}
  filterConfigs={filterConfigs}
  // ... other props
/>
```

### 2. Filters with Icons

```typescript
import { User, Shield, Clock, CheckCircle } from "lucide-react";

const filterConfigs = [
  {
    columnId: "status",
    title: "User Status",
    options: [
      { label: "Active", value: "active", icon: User },
      { label: "Blocked", value: "blocked", icon: Shield },
      { label: "Pending", value: "pending", icon: Clock },
      { label: "Verified", value: "verified", icon: CheckCircle },
    ],
  },
];
```

### 3. Multiple Filter Columns

```typescript
const filterConfigs = [
  {
    columnId: "status",
    title: "Status",
    options: [
      { label: "Published", value: "published" },
      { label: "Draft", value: "draft" },
      { label: "Archived", value: "archived" },
    ],
  },
  {
    columnId: "category",
    title: "Category", 
    options: [
      { label: "Technology", value: "tech" },
      { label: "Business", value: "business" },
      { label: "Design", value: "design" },
    ],
  },
  {
    columnId: "priority",
    title: "Priority",
    options: [
      { label: "High", value: "high" },
      { label: "Medium", value: "medium" },
      { label: "Low", value: "low" },
    ],
  },
];
```

## Real-World Examples

### Student Management System

```typescript
const studentFilterConfigs = [
  {
    columnId: "grade",
    title: "Grade Level",
    options: [
      { label: "Freshman", value: "9" },
      { label: "Sophomore", value: "10" },
      { label: "Junior", value: "11" },
      { label: "Senior", value: "12" },
    ],
  },
  {
    columnId: "status",
    title: "Enrollment Status",
    options: [
      { label: "Enrolled", value: "enrolled" },
      { label: "Withdrawn", value: "withdrawn" },
      { label: "Graduated", value: "graduated" },
      { label: "Suspended", value: "suspended" },
    ],
  },
  {
    columnId: "department",
    title: "Department",
    options: [
      { label: "Science", value: "science" },
      { label: "Mathematics", value: "math" },
      { label: "English", value: "english" },
      { label: "History", value: "history" },
      { label: "Arts", value: "arts" },
    ],
  },
];
```

### E-commerce Orders

```typescript
import { Package, Truck, CheckCircle, XCircle } from "lucide-react";

const orderFilterConfigs = [
  {
    columnId: "status",
    title: "Order Status",
    options: [
      { label: "Pending", value: "pending", icon: Package },
      { label: "Shipped", value: "shipped", icon: Truck },
      { label: "Delivered", value: "delivered", icon: CheckCircle },
      { label: "Cancelled", value: "cancelled", icon: XCircle },
    ],
  },
  {
    columnId: "paymentStatus",
    title: "Payment",
    options: [
      { label: "Paid", value: "paid" },
      { label: "Pending", value: "pending" },
      { label: "Failed", value: "failed" },
      { label: "Refunded", value: "refunded" },
    ],
  },
  {
    columnId: "shippingMethod",
    title: "Shipping",
    options: [
      { label: "Standard", value: "standard" },
      { label: "Express", value: "express" },
      { label: "Overnight", value: "overnight" },
      { label: "Pickup", value: "pickup" },
    ],
  },
];
```

### Project Management

```typescript
import { AlertCircle, Clock, CheckCircle, Pause } from "lucide-react";

const projectFilterConfigs = [
  {
    columnId: "status",
    title: "Project Status",
    options: [
      { label: "Active", value: "active", icon: Clock },
      { label: "Completed", value: "completed", icon: CheckCircle },
      { label: "On Hold", value: "on-hold", icon: Pause },
      { label: "At Risk", value: "at-risk", icon: AlertCircle },
    ],
  },
  {
    columnId: "priority",
    title: "Priority",
    options: [
      { label: "Critical", value: "critical" },
      { label: "High", value: "high" },
      { label: "Medium", value: "medium" },
      { label: "Low", value: "low" },
    ],
  },
  {
    columnId: "team",
    title: "Team",
    options: [
      { label: "Frontend", value: "frontend" },
      { label: "Backend", value: "backend" },
      { label: "DevOps", value: "devops" },
      { label: "QA", value: "qa" },
    ],
  },
];
```

## Migration from Legacy Format

### Before (Legacy - still supported)

```typescript
<DataTable
  filterOptions={{
    statuses: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
    priorities: [
      { label: "High", value: "high" },
      { label: "Low", value: "low" },
    ],
  }}
/>
```

### After (New Dynamic Format)

```typescript
const filterConfigs = [
  {
    columnId: "status", // Maps to the column
    title: "Status",   // Display title
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
  {
    columnId: "priority",
    title: "Priority",
    options: [
      { label: "High", value: "high" },
      { label: "Low", value: "low" },
    ],
  },
];

<DataTable
  filterConfigs={filterConfigs}
/>
```

## Benefits of Dynamic Filtering

1. **Flexibility**: Configure filters for any column
2. **Scalability**: Add/remove filters without modifying component code
3. **Reusability**: Same component works for different data types
4. **Maintainability**: Filter configuration is separate from component logic
5. **Type Safety**: Full TypeScript support for filter configurations

## Complete Example

See `src/components/shared/data-table/dynamic-filtering-example.tsx` for a complete working example with multiple dynamic filters.
