# Multiple Filters Troubleshooting Guide

## Issue: "No results found" when applying multiple filters

This issue typically occurs when there's a mismatch between client-side and server-side filtering logic.

## Root Causes & Solutions

### 1. Missing `filterFn` in Column Definitions

**Problem**: TanStack Table tries to apply client-side filtering even with `manualFiltering: true`

**Solution**: Add `filterFn` to all filterable columns:

```typescript
{
  accessorKey: "status",
  header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
  cell: ({ row }) => <Badge>{row.getValue("status")}</Badge>,
  filterFn: (row, id, value) => {
    return value.includes(row.getValue(id));
  },
},
```

### 2. Server-Side Filtering Logic Issues

**Problem**: Server doesn't handle multiple filters correctly

**Check your API endpoint**:
```typescript
// ❌ Wrong - filters might overwrite each other
let filteredData = allData;
params.columnFilters.forEach(filter => {
  filteredData = allData.filter(item => /* filter logic */);
});

// ✅ Correct - filters are cumulative
let filteredData = allData;
params.columnFilters.forEach(filter => {
  filteredData = filteredData.filter(item => /* filter logic */);
});
```

### 3. Data Type Mismatches

**Problem**: Filter values don't match data types

**Common issues**:
- Numbers stored as strings: `"1"` vs `1`
- Case sensitivity: `"Active"` vs `"active"`
- Array vs single values

**Solution**:
```typescript
// Handle type conversion
filterFn: (row, id, value) => {
  return value.includes(String(row.getValue(id)));
},

// Or in your API:
if (filter.id === "year") {
  filteredData = filteredData.filter(item => 
    filter.value.includes(String(item.year))
  );
}
```

### 4. Client-Side Interference

**Problem**: TanStack Table applies client-side filtering despite `manualFiltering: true`

**Solution**: Ensure proper table configuration:
```typescript
const table = useReactTable({
  // ... other config
  manualFiltering: true,
  manualPagination: true,
  manualSorting: true,
  // Don't include these when using manual filtering:
  getFilteredRowModel: undefined,
  getFacetedRowModel: undefined,
  getFacetedUniqueValues: undefined,
});
```

## Debugging Steps

### 1. Use the Debug Component

Use `src/components/shared/data-table/debug-filtering-example.tsx` to test filtering:

```typescript
import { DebugFilteringExample } from "@/components/shared/data-table/debug-filtering-example";

// Add to your page to test
<DebugFilteringExample />
```

### 2. Check Browser Console

The debug component logs detailed information:
- Filter parameters being sent
- Data before/after each filter
- Final results

### 3. Verify API Response

Check your API endpoint:
```bash
# Test with curl
curl -X POST http://localhost:3000/api/students \
  -H "Content-Type: application/json" \
  -d '{
    "pagination": {"pageIndex": 0, "pageSize": 10},
    "sorting": [],
    "columnFilters": [
      {"id": "status", "value": ["active"]},
      {"id": "grade", "value": ["A", "B"]}
    ]
  }'
```

### 4. Add Logging to Your API

```typescript
export async function POST(request: NextRequest) {
  const { columnFilters } = await request.json();
  
  console.log("📥 Received filters:", columnFilters);
  
  let filteredData = allData;
  
  columnFilters.forEach((filter, index) => {
    console.log(`🔧 Applying filter ${index + 1}:`, filter);
    const beforeCount = filteredData.length;
    
    // Apply filter logic here
    
    console.log(`   ✅ ${beforeCount} → ${filteredData.length} items`);
  });
  
  console.log("🎯 Final count:", filteredData.length);
  
  return NextResponse.json({ data: filteredData, pageCount: ... });
}
```

## Common Filter Patterns

### Multiple Select Filters (OR within, AND between)

```typescript
// Status: [active, inactive] AND Grade: [A, B]
// Should return: (active OR inactive) AND (A OR B)

columnFilters.forEach(filter => {
  if (Array.isArray(filter.value) && filter.value.length > 0) {
    filteredData = filteredData.filter(item => 
      filter.value.includes(item[filter.id])
    );
  }
});
```

### Text Search + Faceted Filters

```typescript
columnFilters.forEach(filter => {
  if (filter.id === "name" && typeof filter.value === "string") {
    // Text search
    filteredData = filteredData.filter(item => 
      item.name.toLowerCase().includes(filter.value.toLowerCase())
    );
  } else if (Array.isArray(filter.value) && filter.value.length > 0) {
    // Faceted filter
    filteredData = filteredData.filter(item => 
      filter.value.includes(item[filter.id])
    );
  }
});
```

## Quick Fixes

### Fix 1: Update Column Definitions

Add `filterFn` to all filterable columns in your `columns.tsx`:

```typescript
// For each filterable column, add:
filterFn: (row, id, value) => {
  return value.includes(row.getValue(id));
},
```

### Fix 2: Verify API Logic

Ensure your API applies filters cumulatively:

```typescript
// ✅ Correct approach
let result = allData;
filters.forEach(filter => {
  result = result.filter(/* filter logic */);
});
```

### Fix 3: Check Data Types

Ensure filter values match your data:

```typescript
// If your data has numbers but filters send strings:
filterFn: (row, id, value) => {
  return value.includes(String(row.getValue(id)));
},
```

## Testing Checklist

- [ ] Single filter works correctly
- [ ] Multiple filters of same type work (e.g., multiple status values)
- [ ] Multiple filters of different types work (e.g., status + grade)
- [ ] Text search + faceted filters work together
- [ ] Clearing filters shows all data
- [ ] Pagination works with filters
- [ ] API logs show correct filter application

## Still Having Issues?

1. Use the debug component to see exact filter values
2. Check browser network tab for API requests
3. Verify your data structure matches filter expectations
4. Test with minimal data set first
5. Check for JavaScript errors in console
