"use client";

import * as React from "react";
import { ColumnDef, ColumnFiltersState, PaginationState, SortingState } from "@tanstack/react-table";
import { DataTable } from "./data-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "./data-table-column-header";
import { User, Shield, GraduationCap } from "lucide-react";

// Simple test data
interface TestStudent {
  id: string;
  name: string;
  status: string;
  grade: string;
  department: string;
}

// Filter configurations
const filterConfigs = [
  {
    columnId: "status",
    title: "Status",
    options: [
      { label: "Active", value: "active", icon: User },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended", icon: Shield },
      { label: "Graduated", value: "graduated", icon: GraduationCap },
    ],
  },
  {
    columnId: "grade",
    title: "Grade",
    options: [
      { label: "A", value: "A" },
      { label: "B", value: "B" },
      { label: "C", value: "C" },
      { label: "D", value: "D" },
      { label: "F", value: "F" },
    ],
  },
  {
    columnId: "department",
    title: "Department",
    options: [
      { label: "Computer Science", value: "cs" },
      { label: "Mathematics", value: "math" },
      { label: "Physics", value: "physics" },
      { label: "Chemistry", value: "chemistry" },
    ],
  },
];

// Columns with proper filterFn
const columns: ColumnDef<TestStudent>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return <Badge variant="outline">{status}</Badge>;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "grade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Grade" />
    ),
    cell: ({ row }) => {
      const grade = row.getValue("grade") as string;
      return <Badge variant="secondary">{grade}</Badge>;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "department",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Department" />
    ),
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
];

// Mock API with detailed logging
async function fetchStudentsWithLogging(params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{ data: TestStudent[]; pageCount: number }> {
  console.log("🔍 Fetching data with params:", params);
  console.log("📊 Column filters:", params.columnFilters);
  
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Create test data
  const allStudents: TestStudent[] = [
    { id: "1", name: "Alice Johnson", status: "active", grade: "A", department: "cs" },
    { id: "2", name: "Bob Smith", status: "inactive", grade: "B", department: "math" },
    { id: "3", name: "Charlie Brown", status: "active", grade: "A", department: "physics" },
    { id: "4", name: "Diana Prince", status: "suspended", grade: "C", department: "cs" },
    { id: "5", name: "Eve Wilson", status: "graduated", grade: "A", department: "chemistry" },
    { id: "6", name: "Frank Miller", status: "active", grade: "B", department: "math" },
    { id: "7", name: "Grace Lee", status: "inactive", grade: "D", department: "physics" },
    { id: "8", name: "Henry Davis", status: "active", grade: "A", department: "cs" },
    { id: "9", name: "Ivy Chen", status: "graduated", grade: "A", department: "chemistry" },
    { id: "10", name: "Jack Wilson", status: "active", grade: "C", department: "math" },
  ];

  console.log("📝 Starting with", allStudents.length, "students");
  
  // Apply filters step by step with logging
  let filteredStudents = allStudents;
  
  params.columnFilters.forEach((filter, index) => {
    console.log(`🔧 Applying filter ${index + 1}:`, filter);
    
    if (filter.value) {
      const beforeCount = filteredStudents.length;
      
      if (filter.id === "name" && typeof filter.value === "string") {
        filteredStudents = filteredStudents.filter(student => 
          student.name.toLowerCase().includes(filter.value.toLowerCase())
        );
      } else if (Array.isArray(filter.value) && filter.value.length > 0) {
        filteredStudents = filteredStudents.filter(student => 
          filter.value.includes(student[filter.id as keyof TestStudent])
        );
      }
      
      const afterCount = filteredStudents.length;
      console.log(`   ✅ Filter applied: ${beforeCount} → ${afterCount} students`);
    } else {
      console.log(`   ⏭️ Filter skipped (no value)`);
    }
  });

  console.log("🎯 Final filtered count:", filteredStudents.length);
  console.log("📋 Filtered students:", filteredStudents.map(s => s.name));

  // Apply pagination
  const { pageIndex, pageSize } = params.pagination;
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedStudents = filteredStudents.slice(start, end);

  console.log(`📄 Pagination: page ${pageIndex + 1}, showing ${paginatedStudents.length} of ${filteredStudents.length}`);

  return {
    data: paginatedStudents,
    pageCount: Math.ceil(filteredStudents.length / pageSize),
  };
}

export function DebugFilteringExample() {
  const [data, setData] = React.useState<TestStudent[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pageCount, setPageCount] = React.useState(-1);
  
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 5,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  // Log filter changes
  React.useEffect(() => {
    console.log("🔄 Column filters changed:", columnFilters);
  }, [columnFilters]);

  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchStudentsWithLogging({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
        console.log("✅ Data loaded successfully");
      } catch (error) {
        console.error("❌ Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Debug Filtering Example</h1>
        <p className="text-muted-foreground">
          Open browser console to see detailed filtering logs. Try applying multiple filters.
        </p>
        
        {/* Debug info */}
        <div className="mt-4 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">Current Filters:</h3>
          <pre className="text-sm">
            {JSON.stringify(columnFilters, null, 2)}
          </pre>
        </div>
      </div>
      
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search students by name..."
        enableBulkActions={true}
        bulkActions={[
          {
            key: "log",
            label: "Log Selected",
            onClick: (selectedRows) => {
              console.log("Selected rows:", selectedRows);
            },
          },
        ]}
      />
    </div>
  );
}
